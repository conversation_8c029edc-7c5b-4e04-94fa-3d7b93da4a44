import { useEffect, useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/contexts/AuthContext'

interface OnboardingGuardProps {
  children: React.ReactNode
}

export const OnboardingGuard = ({ children }: OnboardingGuardProps) => {
  const { user, loading: authLoading } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const [loading, setLoading] = useState(true)
  const [onboardingChecked, setOnboardingChecked] = useState(false)

  useEffect(() => {
    if (authLoading || !user) {
      setLoading(false)
      return
    }

    checkOnboardingStatus()
  }, [user, authLoading, location.pathname])

  const checkOnboardingStatus = async () => {
    if (!user) return

    // Skip onboarding check for onboarding pages themselves
    if (location.pathname.startsWith('/onboarding/')) {
      setLoading(false)
      setOnboardingChecked(true)
      return
    }

    try {
      const { data: onboardingData, error } = await supabase
        .rpc('get_employee_onboarding_status', {
          p_user_id: user.id
        })

      if (error) {
        console.error('Error checking onboarding status:', error)
        // If function fails, allow access (might be admin/HR user)
        setLoading(false)
        setOnboardingChecked(true)
        return
      }

      if (onboardingData && !onboardingData.can_login) {
        // User needs to complete onboarding
        console.log('Redirecting to onboarding:', onboardingData)
        
        switch (onboardingData.onboarding_status) {
          case 'pending':
            navigate('/onboarding/contract', { 
              state: { contractData: onboardingData },
              replace: true 
            })
            return
          case 'contract_accepted':
            navigate('/onboarding/documents', { 
              state: { 
                employeeId: onboardingData.employee_id, 
                contractId: onboardingData.contract_id, 
                requiredDocs: onboardingData.required_documents 
              },
              replace: true 
            })
            return
          case 'docs_uploaded':
            navigate('/onboarding/waiting', { replace: true })
            return
        }
      }

      setLoading(false)
      setOnboardingChecked(true)
    } catch (error) {
      console.error('Onboarding check error:', error)
      setLoading(false)
      setOnboardingChecked(true)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return <>{children}</>
  }

  if (!onboardingChecked) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <>{children}</>
}
