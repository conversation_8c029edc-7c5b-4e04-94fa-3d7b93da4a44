import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import { <PERSON>Left } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { usePermissions } from "@/hooks/usePermissions";
export default function EditUser() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
   const { hasPermission, loading: permissionsLoading } = usePermissions();
  const [formData, setFormData] = useState({
    first_name: "",
    middle_name: "",
    last_name: "",
    personal_email: "",
    phone: "",
    date_of_birth: "",
    gender: "",
    city: "",
    state: "",
    address: "",
    pincode: "",
    role: "",
    company_email: "",
    is_active: true
  })

  useEffect(() => {
    if (id) {
      fetchUser()
    }
  }, [id])

  const fetchUser = async () => {
    try {
      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', id)
        .single()

      if (profileError) throw profileError

      // Get user role
      const { data: userRole } = await supabase
        .from('user_roles')
        .select('roles(name)')
        .eq('user_id', id)
        .single()

      // Get employee data if exists
      const { data: employee } = await supabase
        .from('employees')
        .select('company_email')
        .eq('user_id', id)
        .single()

      setFormData({
        first_name: profile.first_name || "",
        middle_name: profile.middle_name || "",
        last_name: profile.last_name || "",
        personal_email: profile.personal_email || "",
        phone: profile.phone || "",
        date_of_birth: profile.date_of_birth || "",
        gender: profile.gender || "",
        city: profile.city || "",
        state: profile.state || "",
        address: profile.address || "",
        pincode: profile.pincode || "",
        role: userRole?.roles?.name || "",
        company_email: employee?.company_email || "",
        is_active: profile.is_active
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch user details",
        variant: "destructive",
      })
      navigate('/users')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      // Update user profile
      const profileData: any = {
        first_name: formData.first_name,
        middle_name: formData.middle_name || null,
        last_name: formData.last_name,
        personal_email: formData.personal_email,
        phone: formData.phone || null,
        date_of_birth: formData.date_of_birth || null,
        city: formData.city || null,
        state: formData.state || null,
        address: formData.address || null,
        pincode: formData.pincode || null,
        is_active: formData.is_active,
        updated_at: new Date().toISOString()
      }

      if (formData.gender && ['male', 'female', 'other'].includes(formData.gender)) {
        profileData.gender = formData.gender
      }

      const { error: profileError } = await supabase
        .from('user_profiles')
        .update(profileData)
        .eq('id', id)

      if (profileError) throw profileError

      // Update employee company email if exists
      if (formData.company_email) {
        const { error: employeeError } = await supabase
          .from('employees')
          .update({ company_email: formData.company_email })
          .eq('user_id', id)

        if (employeeError) console.warn('Employee update failed:', employeeError)
      }

      toast({
        title: "Success",
        description: "User updated successfully",
      })
      navigate('/users')
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </Layout>
    )
  }
   if (permissionsLoading) {
    return <Layout><div>Loading...</div></Layout>;
  }
  if (!hasPermission('users', 'edit')) {
  return (
    <Layout>
      <div className="p-8 text-center text-red-600">
        <h1>Access Denied</h1>
        <p>You do not have permission to edit users.</p>
      </div>
    </Layout>
  );
}

  return (
    <Layout>
      <div className="mx-auto space-y-1 p-2 md:p-2">
        <div className="flex flex-col sm:flex-row bg-[#4A9782] justify-between items-start sm:items-center gap-4  dark:bg-gray-800 rounded-lg p-6 text-white shadow-sm">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/users')} className="hidden sm:flex">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl md:text-xl font-bold tracking-tight">Edit User</h1>
              <p className="text-muted-foreground mt-1 text-white">Update user information</p>
            </div>
          </div>
          <Button variant="outline" onClick={() => navigate('/users')} className="sm:hidden w-full">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
            {/* Personal Information */}
            <Card className="border-0 shadow-md">
              <CardHeader className="border-b bg-muted/30 rounded-t-lg">
                <CardTitle className="text-lg font-semibold">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="first_name" className="text-sm font-medium">
                      First Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="first_name"
                      value={formData.first_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, first_name: e.target.value }))}
                      className="mt-1.5"
                      placeholder="Enter first name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="middle_name">Middle Name</Label>
                    <Input
                      id="middle_name"
                      value={formData.middle_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, middle_name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="last_name" className="text-sm font-medium">
                      Last Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="last_name"
                      value={formData.last_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, last_name: e.target.value }))}
                      className="mt-1.5"
                      placeholder="Enter last name"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="personal_email" className="text-sm font-medium">
                      Personal Email <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="personal_email"
                      type="email"
                      value={formData.personal_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, personal_email: e.target.value }))}
                      className="mt-1.5"
                      placeholder="Enter personal email"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date_of_birth">Date of Birth</Label>
                    <Input
                      id="date_of_birth"
                      type="date"
                      value={formData.date_of_birth}
                      onChange={(e) => setFormData(prev => ({ ...prev, date_of_birth: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="gender">Gender</Label>
                    <Select value={formData.gender} onValueChange={(value) => setFormData(prev => ({ ...prev, gender: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Address & Account Details */}
            <Card className="border-0 shadow-md">
              <CardHeader className="border-b bg-muted/30 rounded-t-lg">
                <CardTitle className="text-lg font-semibold">Address & Account Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div>
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) => setFormData(prev => ({ ...prev, state: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pincode">Pincode</Label>
                    <Input
                      id="pincode"
                      value={formData.pincode}
                      onChange={(e) => setFormData(prev => ({ ...prev, pincode: e.target.value }))}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="role">Role</Label>
                  <Input
                    id="role"
                    value={formData.role}
                    disabled
                    className="bg-gray-100"
                  />
                  <p className="text-xs text-gray-500 mt-1">Role cannot be changed after creation</p>
                </div>

                {formData.company_email && (
                  <div>
                    <Label htmlFor="company_email">Company Email</Label>
                    <Input
                      id="company_email"
                      type="email"
                      value={formData.company_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, company_email: e.target.value }))}
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="border-0 shadow-md">
            <CardContent className="flex flex-col sm:flex-row justify-end gap-4 py-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/users')}
                className="w-full sm:w-auto order-1 sm:order-none"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={saving}
                className="w-full sm:w-auto bg-primary hover:bg-primary/90"
              >
                {saving ? "Updating..." : "Update User"}
              </Button>
            </CardContent>
          </Card>
        </form>
      </div>
    </Layout>
  )
}