import { useState, useEffect } from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { Plus, Search, Edit, Trash2, Eye, MoreHorizontal, ChevronLeft, ChevronRight } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { usePermissions } from "@/hooks/usePermissions"


interface User {
  id: string
  first_name: string
  last_name: string
  personal_email: string
  phone: string
  is_active: boolean
  created_at: string
  user_roles: {
    roles: {
      name: string
    }
  }[]
  employees: {
    employee_code: string
    company_email: string
  } | null
}

export default function Users() {
  const [users, setUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const { toast } = useToast()
  const navigate = useNavigate()
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions()
  
  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select(`
          id,
          first_name,
          last_name,
          personal_email,
          phone,
          is_active,
          created_at
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Fetch user roles and employees separately to avoid complex joins
      const userIds = data?.map(user => user.id) || []

      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select(`
          user_id,
          roles (
            name
          )
        `)
        .in('user_id', userIds)
        .eq('is_active', true)

      const { data: employees, error: employeesError } = await supabase
        .from('employees')
        .select(`
          user_id,
          employee_code,
          company_email
        `)
        .in('user_id', userIds)
        .eq('is_active', true)

      if (rolesError) throw rolesError
      if (employeesError) throw employeesError

      // Combine the data
      const combinedData = data?.map(user => ({
        ...user,
        user_roles: userRoles?.filter(role => role.user_id === user.id) || [],
        employees: employees?.find(emp => emp.user_id === user.id) || null
      })) || []

      setUsers(combinedData)
    } catch (error) {
      console.error('Error fetching users:', error)
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredUsers = users.filter(user =>
    user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.personal_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.employees?.employee_code?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Pagination calculations
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage)

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1)
  }, [searchTerm])

  const handleView = (user: User) => {
    setSelectedUser(user)
    setViewDialogOpen(true)
  }

  const handleEdit = (userId: string) => {
    navigate(`/users/edit/${userId}`)
  }

  const handleDelete = async (userId: string) => {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        const { error } = await supabase
          .from('user_profiles')
          .update({ is_deleted: true })
          .eq('id', userId)

        if (error) throw error

        toast({
          title: "Success",
          description: "User deleted successfully",
        })
        fetchUsers()
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete user",
          variant: "destructive",
        })
      }
    }
  }

  if (loading || permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!canAccess('users')) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">Access Denied: You don't have permission to view users.</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6 p-4 md:p-6  mx-auto">


        <Card className="shadow-md border-0">
          <CardHeader className="pb-4 bg-[#D25D5D] rounded-t-lg">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-white">Users</h1>
                <p className="text-muted-foreground mt-1 text-white">Manage user accounts and roles</p>
              </div>
              <div className="flex items-center space-x-2 w-full sm:w-auto">
                <div className="relative w-full sm:w-72">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-full"
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="bg-muted/20 rounded-md px-4 py-2 flex items-center gap-2 text-white">
                    <div className="text-sm font-medium">Total Users:</div>
                    <div className="text-sm font-semibold">{users.length}</div>
                  </div>
                  {hasPermission('users', 'add') && (
                    <Link to="/users/add">
                      <Button className="bg-white text-[#000] hover:bg-white/90">
                        <Plus className="h-4 w-4 mr-2" />
                        Add User
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {/* Mobile Card View */}
            <div className="block md:hidden">
              {paginatedUsers.map((user, index) => (
                <div key={user.id} className="border-b p-4 space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground font-medium">#{startIndex + index + 1}</span>
                        <h3 className="font-medium">{user.first_name} {user.last_name}</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">{user.personal_email}</p>
                      {user.employees?.company_email && (
                        <p className="text-sm text-muted-foreground">{user.employees.company_email}</p>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(user)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        {hasPermission('users', 'edit') && (
                          <DropdownMenuItem onClick={() => handleEdit(user.id)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {hasPermission('users', 'delete') && (
                          <DropdownMenuItem onClick={() => handleDelete(user.id)} className="text-red-600">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {user.user_roles.map((userRole, index) => (
                      <Badge key={index} variant="secondary">
                        {userRole.roles.name}
                      </Badge>
                    ))}
                    <Badge variant={user.is_active ? "default" : "secondary"}>
                      {user.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  {user.employees?.employee_code && (
                    <p className="text-sm">Code: {user.employees.employee_code}</p>
                  )}
                  {user.phone && (
                    <p className="text-sm">Phone: {user.phone}</p>
                  )}
                </div>
              ))}
              {paginatedUsers.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No users found
                </div>
              )}
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block">
              <Table className="border-t">
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="font-semibold w-14">#</TableHead>
                    <TableHead className="font-semibold">Name</TableHead>
                    <TableHead className="font-semibold">Email</TableHead>
                    <TableHead className="font-semibold">Phone</TableHead>
                    <TableHead className="font-semibold">Role</TableHead>
                    <TableHead className="font-semibold">Employee Code</TableHead>
                    <TableHead className="font-semibold">Status</TableHead>
                    <TableHead className="font-semibold text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedUsers.map((user, index) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium text-muted-foreground w-14">
                        {startIndex + index + 1}
                      </TableCell>
                      <TableCell className="font-medium">
                        {user.first_name} {user.last_name}
                      </TableCell>
                      <TableCell>
                        <div>
                          {user.personal_email && (
                            <div className="text-sm">{user.personal_email}</div>
                          )}
                          {user.employees?.company_email && (
                            <div className="text-sm text-muted-foreground">
                              {user.employees.company_email}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{user.phone || '-'}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.user_roles.map((userRole, index) => (
                            <Badge key={index} variant="secondary">
                              {userRole.roles.name}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>{user.employees?.employee_code || '-'}</TableCell>
                      <TableCell>
                        <Badge variant={user.is_active ? "default" : "secondary"}>
                          {user.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="sm" onClick={() => handleView(user)} className="h-8 w-8 p-0" title="View">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {hasPermission('users', 'edit') && (
                            <Button variant="ghost" size="sm" onClick={() => handleEdit(user.id)} className="h-8 w-8 p-0" title="Edit">
                              <Edit className="h-4 w-4 text-blue-500" />
                            </Button>
                          )}
                          {hasPermission('users', 'delete') && (
                            <Button variant="ghost" size="sm" onClick={() => handleDelete(user.id)} className="h-8 w-8 p-0" title="Delete">
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {paginatedUsers.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        No users found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>

          {/* Pagination */}
          {filteredUsers.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-4 border-t">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Show</span>
                <Select value={itemsPerPage.toString()} onValueChange={(value) => { setItemsPerPage(parseInt(value)); setCurrentPage(1); }}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span>of {filteredUsers.length} users</span>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNumber = i + 1;
                    if (totalPages > 5) {
                      if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }
                    }

                    return (
                      <Button
                        key={pageNumber}
                        variant={currentPage === pageNumber ? "default" : "outline"}
                        size="sm"
                        className="w-8 h-8 p-0"
                        onClick={() => setCurrentPage(pageNumber)}
                      >
                        {pageNumber}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </div>
            </div>
          )}
        </Card>

        {/* View User Dialog */}
        <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>User Details</DialogTitle>
            </DialogHeader>
            {selectedUser && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Name</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedUser.first_name} {selectedUser.last_name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Personal Email</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedUser.personal_email || '-'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Company Email</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedUser.employees?.company_email || '-'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Phone</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedUser.phone || '-'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Employee Code</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedUser.employees?.employee_code || '-'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <div className="mt-1">
                      <Badge variant={selectedUser.is_active ? "default" : "secondary"}>
                        {selectedUser.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Roles</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedUser.user_roles.map((userRole, index) => (
                      <Badge key={index} variant="secondary">
                        {userRole.roles.name}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Created At</label>
                  <p className="text-sm text-muted-foreground">
                    {new Date(selectedUser.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  )
}