import React, { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { <PERSON><PERSON>eft, ArrowRight, Save, Check, User, Mail, Briefcase, CreditCard, Clock, Shield, FileText } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/integrations/supabase/client"
import { usePermissions } from "@/hooks/usePermissions";
// Step Components
import PersonalInfoStep from "@/components/employee/PersonalInfoStep"
import AuthenticationStep from "@/components/employee/AuthenticationStep"
import WorkDetailsStep from "@/components/employee/WorkDetailsStep"
import BankingInfoStep from "@/components/employee/BankingInfoStep"
import WorkScheduleStep from "@/components/employee/WorkScheduleStep"
import SystemAccessStep from "@/components/employee/SystemAccessStep"
import ContractCreationStep from "@/components/employee/ContractCreationStep"

export interface EmployeeFormData {
  personalInfo: {
    first_name: string
    middle_name: string
    last_name: string
    date_of_birth: Date | null
    gender: string
    address: string
    city: string
    state: string
    pincode: string
    phone: string
    personal_email: string
     biometric_code: string // Add this line
  }
  authDetails: {
    company_email: string
    password: string
    employee_code: string
    hire_date: Date | null
    department_id: string
    designation_id: string
  }
  workDetails: {
    employment_status: string
    onboarding_status: string
  }
  bankingInfo: {
    bank_name: string
    account_holder_name: string
    account_number: string
    ifsc_code: string
    branch_name: string
    is_primary: boolean
  }
  workSchedule: {
    shift_id: string
    work_week_id: string
  }
  systemAccess: {
    role_id: string
    is_active: boolean
  }
  contractInfo: {
    create_contract: boolean
    contract_groups: Array<{
      id?: string
      name: string
      start_date: Date | null
      end_date: Date | null
      contracts: Array<{
        id?: string
        contract_type_id: string
        contract_template_id: string
        start_date: Date | null
        end_date: Date | null
        probation_period: number
        notice_period: number
        overtime_allowed: boolean
        overtime_rate: number
        status: "draft" | "active" | "expired" | "terminated"
        salary_components: Array<{
          salary_component_id: string
          value: number
          component: any
        }>
        selected_holidays: string[]
        selected_leaves: Array<{
          leave_type_id: string
          days_allowed: number
          carry_forward: boolean
          encashable: boolean
          salary_payable: boolean
        }>
      }>
    }>
  }
}

const initialFormData: EmployeeFormData = {
  personalInfo: {
    first_name: "",
    middle_name: "",
    last_name: "",
    date_of_birth: null,
    gender: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
    phone: "",
    personal_email: "",
    biometric_code: "" // Add this line
  },
  authDetails: {
    company_email: "",
    password: "",
    employee_code: "",
    hire_date: null,
    department_id: "",
    designation_id: ""
  },
  workDetails: {
    employment_status: "active",
    onboarding_status: "pending"
  },
  bankingInfo: {
    bank_name: "",
    account_holder_name: "",
    account_number: "",
    ifsc_code: "",
    branch_name: "",
    is_primary: true
  },
  workSchedule: {
    shift_id: "",
    work_week_id: ""
  },
  systemAccess: {
    role_id: "",
    is_active: true
  },
  contractInfo: {
    create_contract: false,
    contract_groups: []
  }
}

const steps = [
  { id: 1, title: "Personal Info", icon: User, description: "Basic personal details" },
  { id: 2, title: "Authentication", icon: Mail, description: "Login credentials" },
  { id: 3, title: "Work Details", icon: Briefcase, description: "Employment information" },
  { id: 4, title: "Banking Info", icon: CreditCard, description: "Bank account details" },
  { id: 5, title: "Work Schedule", icon: Clock, description: "Shifts and work week" },
  { id: 6, title: "System Access", icon: Shield, description: "Roles and permissions" },
  { id: 7, title: "Contract", icon: FileText, description: "Employment contract (optional)" }
]

export default function AddEmployee() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<EmployeeFormData>(initialFormData)
  const [loading, setLoading] = useState(false)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const navigate = useNavigate()
  const { toast } = useToast()
const { hasPermission, loading: permissionsLoading } = usePermissions()


  // Auto-generate employee code
  useEffect(() => {
    generateEmployeeCode()
  }, [])

  const generateEmployeeCode = async () => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('employee_code')
        .order('created_at', { ascending: false })
        .limit(1)

      if (error) throw error

      let nextCode = 'EMP001'
      if (data && data.length > 0) {
        const lastCode = data[0].employee_code
        const lastNumber = parseInt(lastCode.replace('EMP', ''))
        nextCode = `EMP${String(lastNumber + 1).padStart(3, '0')}`
      }

      setFormData(prev => ({
        ...prev,
        authDetails: { ...prev.authDetails, employee_code: nextCode }
      }))
    } catch (error) {
      console.error('Error generating employee code:', error)
    }
  }

  const updateFormData = (step: keyof EmployeeFormData, data: any) => {
    setFormData(prev => ({
      ...prev,
      [step]: { ...prev[step], ...data }
    }))
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.personalInfo.first_name && formData.personalInfo.last_name)
      case 2:
        return !!(formData.authDetails.company_email && formData.authDetails.password && formData.authDetails.hire_date)
      case 3:
        return true // Work details are optional
      case 4:
        return true // Bank details are optional
      case 5:
        return !!(formData.workSchedule.shift_id && formData.workSchedule.work_week_id)
      case 6:
        return !!formData.systemAccess.role_id
      case 7:
        return !formData.contractInfo.create_contract || 
               (formData.contractInfo.contract_groups.length > 0 && 
                formData.contractInfo.contract_groups.some(group => group.contracts.length > 0))
      default:
        return false
    }
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCompletedSteps(prev => [...prev.filter(s => s !== currentStep), currentStep])
      if (currentStep < 7) {
        setCurrentStep(currentStep + 1)
      }
    } else {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      })
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    if (!validateStep(7)) {
      toast({
        title: "Validation Error",
        description: "Please complete all required fields",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      // Create user account
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.authDetails.company_email,
        password: formData.authDetails.password,
        options: {
          data: {
            first_name: formData.personalInfo.first_name,
            last_name: formData.personalInfo.last_name
          }
        }
      })

      if (authError) throw authError
      if (!authData.user) throw new Error('Failed to create user account')

      const userId = authData.user.id

      // Update user profile (trigger already creates basic profile)
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          first_name: formData.personalInfo.first_name,
          middle_name: formData.personalInfo.middle_name || null,
          last_name: formData.personalInfo.last_name,
          personal_email: formData.personalInfo.personal_email || null,
          phone: formData.personalInfo.phone || null,
          date_of_birth: formData.personalInfo.date_of_birth?.toISOString().split('T')[0] || null,
          gender: (["male", "female", "other"].includes(formData.personalInfo.gender) ? formData.personalInfo.gender : null) as "male" | "female" | "other" | null,
          city: formData.personalInfo.city || null,
          state: formData.personalInfo.state || null,
          address: formData.personalInfo.address || null,
          pincode: formData.personalInfo.pincode || null,
          biometric_code: formData.personalInfo.biometric_code || null // Add this line
        })
        .eq('id', userId)

      if (profileError) throw profileError

      // Create employee record
      const { data: employeeData, error: employeeError } = await supabase
        .from('employees')
        .insert({
          user_id: userId,
          employee_code: formData.authDetails.employee_code,
          company_email: formData.authDetails.company_email,
          department_id: formData.authDetails.department_id || null,
          designation_id: formData.authDetails.designation_id || null,
          hire_date: formData.authDetails.hire_date?.toISOString().split('T')[0],
          employment_status: formData.workDetails.employment_status,
          onboarding_status: formData.workDetails.onboarding_status
        } as any)
        .select()
        .single()

      if (employeeError) throw employeeError

      // Create bank details if provided
      if (formData.bankingInfo.bank_name && formData.bankingInfo.account_number) {
        const { error: bankError } = await supabase
          .from('employee_bank_details')
          .insert({
            employee_id: employeeData.id,
            bank_name: formData.bankingInfo.bank_name,
            account_holder_name: formData.bankingInfo.account_holder_name,
            account_number: formData.bankingInfo.account_number,
            ifsc_code: formData.bankingInfo.ifsc_code,
            branch_name: formData.bankingInfo.branch_name || null,
            is_primary: formData.bankingInfo.is_primary
          })

        if (bankError) throw bankError
      }

      // Create employee shift
      const { error: shiftError } = await supabase
        .from('employee_shifts')
        .insert({
          employee_id: employeeData.id,
          shift_id: formData.workSchedule.shift_id,
          work_week_id: formData.workSchedule.work_week_id
        })

      if (shiftError) throw shiftError

      // Update role (replace default Admin with selected role)
      const { error: roleError } = await supabase
        .from('user_roles')
        .update({ role_id: formData.systemAccess.role_id })
        .eq('user_id', userId)

      if (roleError) throw roleError

      // Create contracts if requested
      if (formData.contractInfo.create_contract && formData.contractInfo.contract_groups.length > 0) {
        for (const group of formData.contractInfo.contract_groups) {
          // Create contract group
          const { data: contractGroupData, error: contractGroupError } = await supabase
            .from('contract_groups')
            .insert({
              employee_id: employeeData.id,
              name: group.name,
              start_date: group.start_date?.toISOString().split('T')[0],
              end_date: group.end_date?.toISOString().split('T')[0] || null,
              status: 'active'
            })
            .select()
            .single()

          if (contractGroupError) throw contractGroupError

          // Create contracts in the group
          for (const contract of group.contracts) {
            const totalSalary = contract.salary_components.reduce((sum, sc) => sum + sc.value, 0)
            
            const { data: contractData, error: contractError } = await supabase
              .from('contracts')
              .insert({
                employee_id: employeeData.id,
                contract_group_id: contractGroupData.id,
                contract_type_id: contract.contract_type_id,
                contract_template_id: contract.contract_template_id || null,
                start_date: contract.start_date?.toISOString().split('T')[0],
                end_date: contract.end_date?.toISOString().split('T')[0] || null,
                basic_salary: totalSalary,
                overtime_allowed: contract.overtime_allowed,
                overtime_rate: contract.overtime_rate || null,
                probation_period: contract.probation_period || null,
                notice_period: contract.notice_period || null,
                status: 'draft'
              })
              .select()
              .single()

            if (contractError) throw contractError

            // Create salary components
            if (contract.salary_components.length > 0) {
              const salaryComponentInserts = contract.salary_components.map(sc => ({
                employee_id: employeeData.id,
                salary_component_id: sc.salary_component_id,
                value: sc.value,
                effective_from: contract.start_date?.toISOString().split('T')[0],
                effective_to: contract.end_date?.toISOString().split('T')[0] || null,
                contract_id: contractData.id,
                is_active: true
              }))

              const { error: salaryError } = await supabase
                .from('employee_salary_components')
                .insert(salaryComponentInserts)

              if (salaryError) throw salaryError
            }

            // Create contract holidays
            if (contract.selected_holidays && contract.selected_holidays.length > 0) {
              const holidayInserts = contract.selected_holidays.map(holidayId => ({
                contract_id: contractData.id,
                holiday_id: holidayId,
                is_applicable: true
              }))
              
              const { error: holidayError } = await supabase
                .from('contract_holidays')
                .insert(holidayInserts)
              
              if (holidayError) throw holidayError
            }

            // Create contract leaves
            if (contract.selected_leaves && contract.selected_leaves.length > 0) {
              const leaveInserts = contract.selected_leaves.map(leave => ({
                contract_id: contractData.id,
                leave_type_id: leave.leave_type_id,
                days_allowed: leave.days_allowed,
                carry_forward: leave.carry_forward,
                encashable: leave.encashable,
                salary_payable: leave.salary_payable
              }))
              
              const { error: leaveError } = await supabase
                .from('contract_leaves')
                .insert(leaveInserts)
              
              if (leaveError) throw leaveError
            }

            // Initialize contract acceptance for onboarding flow
            const { error: acceptanceError } = await supabase
              .from('user_contract_acceptance')
              .insert({
                user_id: userId,
                employee_id: employeeData.id,
                contract_id: contractData.id,
                is_accepted: false
              })

            if (acceptanceError) {
              console.error('Contract acceptance initialization error:', acceptanceError)
            }
          }
        }
      }

      toast({
        title: "Success",
        description: "Employee created successfully",
      })
      navigate('/employees')
    } catch (error: any) {
      console.error('Error creating employee:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to create employee",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep data={formData.personalInfo} onChange={(data) => updateFormData('personalInfo', data)} />
      case 2:
        return <AuthenticationStep data={formData.authDetails} onChange={(data) => updateFormData('authDetails', data)} />
      case 3:
        return <WorkDetailsStep data={formData.workDetails} onChange={(data) => updateFormData('workDetails', data)} />
      case 4:
        return <BankingInfoStep data={formData.bankingInfo} onChange={(data) => updateFormData('bankingInfo', data)} />
      case 5:
        return <WorkScheduleStep data={formData.workSchedule} onChange={(data) => updateFormData('workSchedule', data)} />
      case 6:
        return <SystemAccessStep data={formData.systemAccess} onChange={(data) => updateFormData('systemAccess', data)} />
      case 7:
        return <ContractCreationStep data={formData.contractInfo} onChange={(data) => updateFormData('contractInfo', data)} />
      default:
        return null
    }
  }

  const progress = (currentStep / 7) * 100
if (permissionsLoading) {
    return <Layout><div>Loading...</div></Layout>;
  }

  // 👇 ADD THIS PAGE GUARD AT THE TOP 👇
  if (!hasPermission('employees', 'add')) {
    return (
      <Layout>
        <div className="p-8 text-center text-red-600">
          <h2>Access Denied</h2>
          <p>You do not have permission to add new employees.</p>
        </div>
      </Layout>
    );
  }
  return (
    <Layout>
      <div className="mx-auto space-y-1 p-2 md:p-2">
        {/* Header */}
        <div className="flex flex-col sm:flex-row bg-[#4A9782] justify-between items-start sm:items-center gap-4 dark:bg-gray-800 rounded-lg p-6 text-white shadow-sm">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/employees')} className="hidden sm:flex">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl md:text-xl font-bold tracking-tight">Add New Employee</h1>
              <p className="text-muted-foreground mt-1 text-white">Complete employee onboarding process</p>
            </div>
          </div>
          <Button variant="outline" onClick={() => navigate('/employees')} className="sm:hidden w-full">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Employees
          </Button>
        </div>

        <div className="space-y-6">
          {/* Progress Bar */}
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium text-gray-700">Step {currentStep} of 7</span>
                <span className="text-sm text-gray-500">{Math.round(progress)}% Complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </CardContent>
          </Card>

          {/* Step Navigation */}
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                {steps.map((step) => {
                  const Icon = step.icon
                  const isCompleted = completedSteps.includes(step.id)
                  const isCurrent = currentStep === step.id
                  
                  return (
                    <div
                      key={step.id}
                      className={`p-3 rounded-lg border text-center cursor-pointer transition-all ${
                        isCurrent
                          ? 'bg-blue-50 border-blue-200 text-blue-700'
                          : isCompleted
                          ? 'bg-green-50 border-green-200 text-green-700'
                          : 'bg-white border-gray-200 text-gray-500 hover:bg-gray-50'
                      }`}
                      onClick={() => setCurrentStep(step.id)}
                    >
                      <div className="flex items-center justify-center mb-2">
                        {isCompleted ? (
                          <Check className="h-5 w-5" />
                        ) : (
                          <Icon className="h-5 w-5" />
                        )}
                      </div>
                      <div className="text-xs font-medium">{step.title}</div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Step Content */}
          <Card className="border-0 shadow-md">
            <CardHeader className="border-b bg-muted/30 rounded-t-lg">
              <CardTitle className="text-lg font-semibold">{steps[currentStep - 1].title}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              {renderStepContent()}
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <Card className="border-0 shadow-md">
            <CardContent className="flex justify-between py-4">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="px-6"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex gap-3">
                {currentStep === 7 ? (
                  <Button
                    onClick={handleSubmit}
                    disabled={loading}
                    className="px-8 bg-primary hover:bg-primary/90"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {loading ? "Creating..." : "Create Employee"}
                  </Button>
                ) : (
                  <Button onClick={handleNext} className="px-6">
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  )
}